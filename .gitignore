# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
pnpm-lock.yaml

# macOS
.DS_Store
.AppleDouble
.LSOverride
# Thumbnails
._*
# Files that might appear in the root of a volume
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDEs and Editors
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Build Output
build/
dist/
out/
coverage/
*.js.map
*.tsbuildinfo

# Expo specific
.expo/
web-build/
*.jks
*.p8
*.p12
*.key
*.mobileprovision
# Expo Go
.expo-shared/

# iOS
ios/Pods/
ios/build/
ios/*.xcworkspace
ios/*.xcodeproj/project.xcworkspace/
ios/*.xcodeproj/xcuserdata/
*.ipa
*.app
# Xcode - User specific
*.xcuserdatad

# Android
android/app/build/
android/build/
android/.gradle/
android/gradle/
android/app/src/main/assets/app.manifest
android/app/src/main/assets/index.android.bundle
android/app/src/main/java/com/helloworld/MainApplication.java
# Keystore files
*.keystore
*.jks
# Android Studio
.idea/
*.iml
# Buck
buck-out/
\.buckd/
android/app/debug/
android/app/release/

# Other
*.log
.env
.env.*
!.env.example
# Temporary files
*.tmp
*~
# Crash reports
*.crash
